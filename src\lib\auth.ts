import { NextAuthOptions } from "next-auth"
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { signInSchema } from "./validations/auth"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Validate input
          const validatedFields = signInSchema.safeParse({
            email: credentials.email,
            password: credentials.password,
            rememberMe: false,
          })

          if (!validatedFields.success) {
            return null
          }

          // TODO: Replace with actual database lookup
          // For now, we'll use mock authentication
          const mockUser = {
            id: "1",
            email: credentials.email,
            name: "<PERSON>",
            role: "admin",
            tenantId: "tenant-1",
          }

          // Mock password check (in real app, compare with hashed password)
          if (credentials.password === "password123") {
            return {
              id: mockUser.id,
              email: mockUser.email,
              name: mockUser.name,
              role: mockUser.role,
              tenantId: mockUser.tenantId,
            }
          }

          return null
        } catch (error) {
          console.error("Authentication error:", error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.tenantId = user.tenantId
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
        session.user.tenantId = token.tenantId as string
      }
      return session
    },
  },
  pages: {
    signIn: "/signin",
    signOut: "/signout",
  },
  secret: process.env.NEXTAUTH_SECRET,
}
