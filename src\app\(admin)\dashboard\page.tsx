"use client"

import { useSession, signOut } from "next-auth/react"
import { useAuthStore } from "@/store/authStore"
import { But<PERSON> } from "@/components/ui/shadcn/button"
import Link from "next/link"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const { user, logout } = useAuthStore()

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/signin" })
    logout()
  }

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="mb-4">You need to be signed in to view this page.</p>
          <Link href="/signin">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Welcome to CLOVER ERP
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                User Management System Dashboard
              </p>
            </div>
            <Button onClick={handleSignOut} variant="outline">
              Sign Out
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Info Card */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Your Profile
              </h2>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Name
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {session.user?.name || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Email
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {session.user?.email || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Role
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {session.user?.role || "user"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Tenant ID
                  </label>
                  <p className="text-gray-900 dark:text-white">
                    {session.user?.tenantId || "N/A"}
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Actions Card */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Quick Actions
              </h2>
              <div className="space-y-3">
                {(session.user?.role === "admin" || session.user?.role === "super_admin") && (
                  <Link href="/users">
                    <Button className="w-full justify-start" variant="outline">
                      Manage Users
                    </Button>
                  </Link>
                )}
                <Link href="/profile">
                  <Button className="w-full justify-start" variant="outline">
                    Edit Profile
                  </Button>
                </Link>
                <Link href="/settings">
                  <Button className="w-full justify-start" variant="outline">
                    Settings
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Technology Stack Info */}
          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-900 dark:text-blue-100">
              Technology Stack Applied
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Frontend
                </h3>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>✅ Next.js 15.2.3 (App Router)</li>
                  <li>✅ shadcn/ui Components</li>
                  <li>✅ Tailwind CSS 4.0</li>
                  <li>✅ Zustand (State Management)</li>
                  <li>✅ React Query (Server State)</li>
                  <li>✅ React Hook Form + Zod</li>
                  <li>✅ NextAuth.js</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Backend
                </h3>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>✅ Next.js API Routes</li>
                  <li>✅ Prisma ORM (Schema Ready)</li>
                  <li>✅ PostgreSQL Support</li>
                  <li>✅ JWT Authentication</li>
                  <li>✅ Route Protection</li>
                  <li>✅ Multi-tenant Architecture</li>
                  <li>✅ RBAC System</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
