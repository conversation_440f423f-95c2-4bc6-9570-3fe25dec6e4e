import { withAuth } from "next-auth/middleware"

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Check if user is authenticated for protected routes
        if (req.nextUrl.pathname.startsWith("/admin")) {
          return token?.role === "admin" || token?.role === "super_admin"
        }
        
        if (req.nextUrl.pathname.startsWith("/api/")) {
          return !!token
        }
        
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    "/admin/:path*",
    "/api/users/:path*",
    "/api/roles/:path*",
    "/api/permissions/:path*",
    "/dashboard/:path*",
  ]
}
